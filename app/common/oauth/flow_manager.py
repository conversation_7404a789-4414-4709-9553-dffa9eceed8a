import secrets
import time
from enum import Enum
from urllib.parse import urlencode

import jwt
from authlib.integrations.httpx_client import AsyncOAuth2Client
from authlib.oauth2.rfc7636 import create_s256_code_challenge

from app.common.helpers.logger import get_logger
from app.common.oauth.exceptions import OAuthStateInvalidError, OAuthTokenError
from app.core.config import config

logger = get_logger()


class OAuthFlowType(str, Enum):
    STANDARD = "standard"
    PKCE = "pkce"


class OAuthFlowManager:
    """Manager to handle OAuth 2.0 flows using a stateless approach"""

    def __init__(
        self, secret_key: str | None = None, state_expiration_seconds: int = 600
    ):
        """
        Initialize with a secret key for JWT encoding/decoding

        Args:
            secret_key: Secret key for JWT encoding/decoding (optional, uses config if not provided)
            state_expiration_seconds: How long the state token remains valid (in seconds), defaults to 10 minutes
        """
        self.secret_key = secret_key or config.secret_key

        if not self.secret_key:
            raise ValueError("Secret key cannot be empty or None.")

        self.state_expiration_seconds = state_expiration_seconds

    def generate_authorization_uri(
        self,
        uid: str,
        client_id: str,
        redirect_uri: str,
        auth_url: str,
        flow_type: OAuthFlowType = OAuthFlowType.PKCE,
        scope: str = "refresh_token full",
    ) -> str:
        """
        Generates uri for OAuth flow with stateless approach
        """
        # Generate state as a JWT token containing necessary info
        state_data = {
            "type": flow_type,
            "ts": int(time.time()),
            "nonce": secrets.token_hex(8),
            "uid": uid,
        }

        # For PKCE, generate and include code_verifier
        if flow_type == OAuthFlowType.PKCE:
            code_verifier = secrets.token_urlsafe(64)
            state_data["cv"] = code_verifier
            code_challenge = create_s256_code_challenge(code_verifier)

        # Encode state as JWT
        state = jwt.encode(state_data, self.secret_key, algorithm="HS256")

        # Base parameters for OAuth flow
        params = {
            "response_type": "code",
            "client_id": client_id,
            "redirect_uri": redirect_uri,
            "scope": scope,
            "state": state,
        }

        # Add PKCE parameters if needed
        if flow_type == OAuthFlowType.PKCE:
            params.update(
                {
                    "code_challenge": code_challenge,
                    "code_challenge_method": "S256",
                }
            )

        # Build the complete authorization URI
        auth_uri = f"{auth_url}?{urlencode(params)}"

        return auth_uri

    def validate_callback_state(
        self, state: str, expected_uid: str
    ) -> tuple[bool, str]:
        """
        Validates the state from callback and returns validation result and code_verifier if applicable

        Args:
            state: The state parameter from the OAuth callback
            expected_uid: The user ID expected to be in the state token

        Returns:
            Tuple of (is_valid, code_verifier) where code_verifier may be empty string if not PKCE
        """
        try:
            payload = jwt.decode(state, self.secret_key, algorithms=["HS256"])

            # Check if state is expired using the configurable expiration time
            current_time = int(time.time())
            if current_time - payload.get("ts", 0) > self.state_expiration_seconds:
                logger.warning("State token expired")
                return False, ""

            # Verify user ID matches
            token_user_id = payload.get("uid", "")
            if token_user_id != str(expected_uid):
                logger.warning(
                    f"User ID mismatch: expected {expected_uid}, got {token_user_id}"
                )
                return False, ""

            # Get flow type
            flow_type = payload.get("type")

            # For PKCE flow, return code_verifier
            if flow_type == OAuthFlowType.PKCE:
                code_verifier = payload.get("cv", "")
                return bool(code_verifier), code_verifier

            # For standard flow, just validate
            return True, ""

        except jwt.InvalidTokenError:
            logger.warning("Invalid state token")
            return False, ""

    def extract_user_id_from_state(self, state: str) -> str | None:
        """
        Extracts the user ID from the state parameter without validation

        Args:
            state: The state parameter from the OAuth callback

        Returns:
            The user ID if the state is valid and not expired, None otherwise
        """
        try:
            payload = jwt.decode(state, self.secret_key, algorithms=["HS256"])

            # Check if state is expired
            current_time = int(time.time())
            if current_time - payload.get("ts", 0) > self.state_expiration_seconds:
                logger.warning("State token expired")
                return None

            return payload.get("uid")

        except jwt.InvalidTokenError:
            logger.warning("Invalid state token")
            return None

    async def exchange_code_for_token(
        self,
        code: str,
        state: str,
        expected_uid: str,
        client_id: str,
        client_secret: str,
        redirect_uri: str,
        token_url: str,
    ) -> dict[str, str]:
        """
        Validates state and exchanges code for token in a single operation

        Args:
            code: The authorization code
            state: The state from callback
            expected_uid: User ID that should match the one encoded in the state token
            client_id: OAuth client ID
            client_secret: OAuth client secret
            redirect_uri: Redirect URI
            token_url: Token endpoint URL

        Returns:
            Dictionary containing token information

        Raises:
            OAuthStateInvalidError: If state validation fails
            OAuthTokenError: If token exchange fails
        """
        # Validate state and get code_verifier if applicable
        is_valid, code_verifier = self.validate_callback_state(state, expected_uid)

        if not is_valid:
            raise OAuthStateInvalidError()

        # Create OAuth client
        client = AsyncOAuth2Client(
            client_id=client_id,
            client_secret=client_secret,
            redirect_uri=redirect_uri,
        )

        try:
            # Prepare token request parameters
            token_params = {
                "url": token_url,
                "code": code,
                "grant_type": "authorization_code",
            }

            # Add code_verifier if PKCE is used
            if code_verifier:
                token_params["code_verifier"] = code_verifier

            # Exchange code for token
            token_data = await client.fetch_token(**token_params)
            return token_data

        except Exception:
            logger.exception("Error fetching token")
            raise OAuthTokenError()

    async def refresh_access_token(
        self,
        refresh_token: str,
        client_id: str,
        client_secret: str,
        token_url: str,
    ) -> dict[str, str]:
        """
        Refreshes an OAuth access token

        Args:
            refresh_token: The refresh token
            client_id: OAuth client ID
            client_secret: OAuth client secret
            token_url: Token endpoint URL

        Returns:
            Dictionary containing token information

        Raises:
            OAuthTokenError: If token refresh fails
        """
        client = AsyncOAuth2Client(
            client_id=client_id,
            client_secret=client_secret,
        )

        try:
            token_data = await client.refresh_token(
                url=token_url,
                refresh_token=refresh_token,
            )
            return token_data

        except Exception:
            logger.exception("Error refreshing token")
            raise OAuthTokenError()
