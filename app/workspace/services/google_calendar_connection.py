import uuid
from datetime import UTC, datetime, timedelta

from sqlalchemy.ext.asyncio import AsyncSession

from app.common.helpers.logger import get_logger
from app.common.oauth.flow_manager import OAuthFlowManager, OAuthFlowType
from app.integrations.adapters.google_calendar.client import (
    GoogleCalendarClient,
)
from app.integrations.types import IntegrationSource
from app.workspace.exceptions import (
    IntegrationConfigError,
    IntegrationCredentialsError,
    IntegrationTokenNotFoundError,
)
from app.workspace.models import IntegrationConfig
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.repositories.integration_user import IntegrationUserRepository
from app.workspace.schemas import (
    GoogleCalendarCredentials,
    GoogleCalendarTokenResponse,
    OrgEnvironment,
)

logger = get_logger()


class GoogleCalendarConnectionService:
    def __init__(
        self,
        db_session: AsyncSession,
        integration_user_repo: IntegrationUserRepository,
        integration_cfg_repo: IntegrationConfigRepository,
        auth_url: str,
        token_url: str,
        redirect_uri: str,
        flow_type: OAuthFlowType,
    ):
        self.db_session = db_session
        self.integration_user_repo = integration_user_repo
        self.integration_cfg_repo = integration_cfg_repo
        self.integration_source = IntegrationSource.GOOGLE_CALENDAR
        self.auth_url = auth_url
        self.token_url = token_url
        self.redirect_uri = redirect_uri
        self.flow_type = flow_type
        self.oauth_flow_manager = OAuthFlowManager()

    async def generate_oauth_authorization_uri(
        self,
        user_id: uuid.UUID,
        environment: OrgEnvironment,
        scope: str = "https://www.googleapis.com/auth/calendar",
    ) -> str:
        _, credentials = await self._get_config_and_credentials(environment)

        if not credentials.client_id or not credentials.client_secret:
            raise IntegrationCredentialsError(
                "Missing Google Calendar client_id and client_secret"
            )

        return self.oauth_flow_manager.generate_authorization_uri(
            uid=str(user_id),
            client_id=credentials.client_id,
            redirect_uri=self.redirect_uri,
            auth_url=self.auth_url,
            flow_type=self.flow_type,
            scope=scope,
        )

    async def process_oauth_callback(
        self,
        user_id: uuid.UUID,
        environment: OrgEnvironment,
        code: str,
        state: str,
    ) -> GoogleCalendarTokenResponse:
        integration_config, credentials = await self._get_config_and_credentials(
            environment
        )

        if not credentials.client_id or not credentials.client_secret:
            raise IntegrationCredentialsError(
                "Missing Google Calendar client_id and client_secret"
            )

        print("-" * 80)
        print("credentials >>>", credentials)
        print("-" * 80)

        token_data = await self.oauth_flow_manager.exchange_code_for_token(
            code=code,
            state=state,
            expected_uid=str(user_id),
            client_id=credentials.client_id,
            client_secret=credentials.client_secret,
            redirect_uri=self.redirect_uri,
            token_url=self.token_url,
        )

        print("-" * 80)
        print("token data >>>", token_data)
        print("-" * 80)

        calendar_client = GoogleCalendarClient(
            credentials={
                "access_token": token_data.get("access_token"),
                "refresh_token": token_data.get("refresh_token"),
                "client_id": credentials.client_id,
                "client_secret": credentials.client_secret,
                "token_uri": self.token_url,
                "scopes": ["https://www.googleapis.com/auth/calendar"],
            }
        )

        user_info = await calendar_client.get_user_info()
        external_user_id = user_info.get("id", str(user_id))

        # Calculate expiration time
        expires_in = token_data.get("expires_in", 3600)
        expires_at = datetime.now(UTC) + timedelta(seconds=expires_in)

        # Check if integration user already exists
        existing_integration_user = (
            await self.integration_user_repo.get_by_user_id_and_config_id(
                user_id=user_id, integration_config_id=integration_config.id
            )
        )

        if existing_integration_user:
            # Update existing integration user
            update_data = {
                "access_token": token_data.get("access_token"),
                "refresh_token": token_data.get("refresh_token"),
                "expires_at": expires_at,
                "external_user_id": external_user_id,
            }

            if "token_type" in token_data:
                update_data["token_type"] = token_data.get("token_type")

            updated_integration_user = await self.integration_user_repo.update(
                existing_integration_user.id, **update_data
            )
            await self.db_session.commit()
            await self.db_session.refresh(updated_integration_user)

            return GoogleCalendarTokenResponse(
                external_user_id=updated_integration_user.external_user_id,
                access_token=updated_integration_user.access_token,
                expires_at=expires_at,
            )
        else:
            # Create new integration user
            integration_user = await self.integration_user_repo.create(
                user_id=user_id,
                integration_config_id=integration_config.id,
                external_user_id=external_user_id,
                access_token=token_data.get("access_token"),
                refresh_token=token_data.get("refresh_token"),
                expires_at=expires_at,
                token_type=token_data.get("token_type", "Bearer"),
            )
            await self.db_session.commit()
            await self.db_session.refresh(integration_user)

            return GoogleCalendarTokenResponse(
                external_user_id=integration_user.external_user_id,
                access_token=integration_user.access_token,
                expires_at=expires_at,
            )

    async def refresh_access_token(
        self,
        integration_user_id: uuid.UUID,
        environment: OrgEnvironment,
    ) -> GoogleCalendarTokenResponse:
        integration_user = await self.integration_user_repo.get_by_id(
            integration_user_id
        )
        if not integration_user:
            raise IntegrationTokenNotFoundError()

        _, credentials = await self._get_config_and_credentials(environment)

        if not credentials.client_id or not credentials.client_secret:
            raise IntegrationCredentialsError(
                "Missing Google Calendar client_id and client_secret"
            )

        token_data = await self.oauth_flow_manager.refresh_access_token(
            refresh_token=integration_user.refresh_token,
            client_id=credentials.client_id,
            client_secret=credentials.client_secret,
            token_url=self.token_url,
        )

        # Calculate expiration time
        expires_in = token_data.get("expires_in", 3600)
        expires_at = datetime.now(UTC) + timedelta(seconds=expires_in)

        # Update integration user with new token
        update_data = {
            "access_token": token_data.get("access_token"),
            "expires_at": expires_at,
        }

        if "refresh_token" in token_data:
            update_data["refresh_token"] = token_data.get("refresh_token")

        if "token_type" in token_data:
            update_data["token_type"] = token_data.get("token_type")

        updated_integration_user = await self.integration_user_repo.update(
            integration_user.id, **update_data
        )
        await self.db_session.commit()
        await self.db_session.refresh(updated_integration_user)

        return GoogleCalendarTokenResponse(
            external_user_id=updated_integration_user.external_user_id,
            access_token=updated_integration_user.access_token,
            expires_at=expires_at,
        )

    async def _get_config_and_credentials(
        self, environment: OrgEnvironment
    ) -> tuple[IntegrationConfig, GoogleCalendarCredentials]:
        integration_config = await self.integration_cfg_repo.get_by_org_and_source(
            org_id=environment.organization_id, source=self.integration_source
        )

        if not integration_config:
            raise IntegrationConfigError(
                f"No Google Calendar integration config found for organization {environment.organization_id}"
            )

        return integration_config, GoogleCalendarCredentials.model_validate(
            integration_config.credentials
        )
